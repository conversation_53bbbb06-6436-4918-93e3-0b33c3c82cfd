#!/usr/bin/env python3
"""
创建完整的initial.xlsx文件
"""

import pandas as pd

def create_complete_initial_excel():
    """创建包含更多元素的initial.xlsx文件"""
    
    # 完整的原料数据
    data = {
        'Element No.': [3, 11, 12, 13, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 56, 57, 58, 59, 60, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83],
        'Element': ['Li', 'Na', 'Mg', 'Al', 'K', 'Ca', 'Sc', 'Ti', 'V', 'Cr', 'Mn', 'Fe', 'Co', 'Ni', 'Cu', 'Zn', 'Sr', 'Y', '<PERSON>r', 'Nb', '<PERSON>', '<PERSON>u', '<PERSON>h', 'Pd', 'Ag', 'Cd', 'In', 'Sn', 'Ba', 'La', '<PERSON>', 'Pr', 'Nd', 'Sm', 'Eu', 'Gd', 'Tb', '<PERSON>y', 'Ho', '<PERSON>r', 'Tm', 'Yb', 'Lu', 'Hf', '<PERSON>', 'W', 'Re', 'Os', 'Ir', 'Pt', 'Au', 'Hg', 'Tl', 'Pb', 'Bi'],
        '<PERSON>io': [2.0, 2.0, 1.0, 2.0, 2.0, 1.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 1.0, 2.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 2.0],
        'Formula': ['Li2CO3', 'Na2CO3', 'MgO', 'Al2O3', 'K2CO3', 'CaCO3', 'Sc2O3', 'TiO2', 'V2O5', 'Cr2O3', 'MnO2', 'Fe2O3', 'CoO', 'NiO', 'CuO', 'ZnO', 'SrCO3', 'Y2O3', 'ZrO2', 'Nb2O5', 'MoO3', 'RuO2', 'Rh2O3', 'PdO', 'Ag2O', 'CdO', 'In2O3', 'SnO2', 'BaCO3', 'La2O3', 'CeO2', 'Pr2O3', 'Nd2O3', 'Sm2O3', 'Eu2O3', 'Gd2O3', 'Tb2O3', 'Dy2O3', 'Ho2O3', 'Er2O3', 'Tm2O3', 'Yb2O3', 'Lu2O3', 'HfO2', 'Ta2O5', 'WO3', 'Re2O7', 'OsO4', 'IrO2', 'PtO2', 'Au2O3', 'HgO', 'Tl2O3', 'PbO', 'Bi2O3'],
        'Molecular Weight': [73.89, 105.99, 40.30, 101.96, 138.21, 100.09, 137.91, 79.87, 181.88, 151.99, 86.94, 159.69, 74.93, 74.69, 79.55, 81.38, 147.63, 225.81, 123.22, 265.81, 143.94, 133.07, 253.81, 122.42, 231.74, 128.41, 277.64, 150.71, 197.34, 325.81, 172.11, 329.81, 336.48, 348.72, 351.93, 362.50, 365.85, 373.00, 377.86, 382.52, 385.87, 394.08, 397.93, 210.49, 441.89, 231.84, 484.41, 254.23, 224.22, 227.08, 441.93, 216.59, 456.76, 223.20, 465.96],
        'CAS': ['554-13-2', '497-19-8', '1309-48-4', '1344-28-1', '584-08-7', '471-34-1', '12060-08-1', '13463-67-7', '1314-62-1', '1308-38-9', '1313-13-9', '1309-37-1', '1307-96-6', '1313-99-1', '1317-38-0', '1314-13-2', '1633-05-2', '1314-36-9', '1314-23-4', '1313-96-8', '1313-27-5', '12036-10-1', '12036-35-0', '1314-08-5', '20667-12-3', '1306-19-0', '1312-43-2', '18282-10-5', '513-77-9', '1312-81-8', '1306-38-3', '12037-29-5', '1313-97-9', '12060-58-1', '1308-96-9', '12064-62-9', '12037-01-3', '1308-87-8', '12055-62-8', '12061-16-4', '12036-44-1', '1314-37-0', '12032-20-1', '12055-23-1', '1314-61-0', '1314-35-8', '1314-68-7', '20816-12-0', '1312-46-5', '1314-15-4', '1304-28-5', '21908-53-2', '1314-32-5', '1317-36-8', '1304-76-3'],
        'Manufacturer': ['aladdin'] * 55,
        'Product No.': [f'{elem}_{i:03d}' for i, elem in enumerate(['Li', 'Na', 'Mg', 'Al', 'K', 'Ca', 'Sc', 'Ti', 'V', 'Cr', 'Mn', 'Fe', 'Co', 'Ni', 'Cu', 'Zn', 'Sr', 'Y', 'Zr', 'Nb', 'Mo', 'Ru', 'Rh', 'Pd', 'Ag', 'Cd', 'In', 'Sn', 'Ba', 'La', 'Ce', 'Pr', 'Nd', 'Sm', 'Eu', 'Gd', 'Tb', 'Dy', 'Ho', 'Er', 'Tm', 'Yb', 'Lu', 'Hf', 'Ta', 'W', 'Re', 'Os', 'Ir', 'Pt', 'Au', 'Hg', 'Tl', 'Pb', 'Bi'])],
        'Purity': [99.0] * 55,
        'Pricing': [100 + i * 10 for i in range(55)],  # 递增价格
        'Excess': [1.0] * 55  # 默认无过量
    }
    
    # 设置一些常用元素的过量
    common_excess = {'Li': 1.05, 'Na': 1.02, 'K': 1.02, 'Bi': 1.01}
    for i, element in enumerate(data['Element']):
        if element in common_excess:
            data['Excess'][i] = common_excess[element]
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 保存为Excel文件
    df.to_excel('initial_complete.xlsx', index=False)
    print(f"✓ 创建了包含 {len(df)} 种元素的完整Excel文件: initial_complete.xlsx")
    
    # 显示前几行
    print("\n前10行数据:")
    print(df.head(10)[['Element', 'Formula', 'Molecular Weight', 'Purity', 'Excess']].to_string())
    
    return df

if __name__ == "__main__":
    create_complete_initial_excel()
