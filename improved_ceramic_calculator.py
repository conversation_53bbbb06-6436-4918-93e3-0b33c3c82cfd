import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import re
from typing import Dict
from tkinter import messagebox, filedialog
import json
import os
import pandas as pd
from datetime import datetime


class ImprovedCeramicCalculator:
    def __init__(self):
        # 设置主题和颜色模式
        self.window = ttk.Window(themename="litera")
        self.window.title("储能陶瓷配料计算器")
        self.window.geometry("1600x1000")  # 增大窗口尺寸
        self.window.state('zoomed')  # 最大化窗口

        # 设置默认字体
        self.default_font = ("Microsoft YaHei", 11)
        self.bold_font = ("Microsoft YaHei", 11, "bold")
        self.large_font = ("Microsoft YaHei", 16, "bold")
        self.small_font = ("Microsoft YaHei", 10)

        # 默认原料信息：(化学式, 纯度, 货号)
        self.raw_materials = {
            'Bi': ('Bi2O3', 0.99, 'BI001'),
            'Na': ('Na2CO3', 0.99, 'NA001'),
            'Ti': ('TiO2', 0.99, 'TI001'),
            'Ta': ('Ta2O5', 0.99, 'TA001'),
            'Ba': ('BaCO3', 0.99, 'BA001'),
            'Ca': ('CaCO3', 0.99, 'CA001'),
            'Zr': ('ZrO2', 0.99, 'ZR001'),
            'Sr': ('SrCO3', 0.99, 'SR001'),
            'La': ('La2O3', 0.99, 'LA001'),
            'Fe': ('Fe2O3', 0.99, 'FE001'),
            'Mg': ('MgO', 0.99, 'MG001'),
            'K': ('K2CO3', 0.99, 'K001'),
            'Li': ('Li2CO3', 0.99, 'LI001'),
            'Al': ('Al2O3', 0.99, 'AL001'),
            'Si': ('SiO2', 0.99, 'SI001'),
            'Nb': ('Nb2O5', 0.99, 'NB001')
        }

        # 过量设置
        self.excess_settings = {}

        # 存储输入框引用
        self.formula_entries = []
        self.mass_entries = []

        self.create_widgets()

    def create_widgets(self):
        # 创建主容器
        self.main_container = ttk.Frame(self.window)
        self.main_container.pack(fill="both", expand=True, padx=20, pady=20)

        # 创建顶部标题栏
        self.create_header()

        # 创建主要内容区域 - 使用PanedWindow实现可调整大小的分割
        self.paned_window = ttk.PanedWindow(self.main_container, orient="horizontal")
        self.paned_window.pack(fill="both", expand=True, pady=(20, 0))

        # 左侧面板 - 输入和设置
        self.left_panel = ttk.Frame(self.paned_window)
        self.paned_window.add(self.left_panel, weight=1)

        # 右侧面板 - 结果显示
        self.right_panel = ttk.Frame(self.paned_window)
        self.paned_window.add(self.right_panel, weight=1)

        self.create_left_panel()
        self.create_right_panel()

    def create_header(self):
        header = ttk.Frame(self.main_container)
        header.pack(fill="x", pady=(0, 20))

        # 标题
        title = ttk.Label(
            header,
            text="储能陶瓷配料计算器",
            font=self.large_font,
            bootstyle="primary"
        )
        title.pack(side="left")

        # 副标题
        subtitle = ttk.Label(
            header,
            text="精确计算化学配料比例 - 支持复杂化学式解析",
            font=self.default_font,
            bootstyle="secondary"
        )
        subtitle.pack(side="left", padx=(20, 0))

    def create_left_panel(self):
        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.left_panel)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # 化学式输入选项卡
        self.formula_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.formula_tab, text="化学式输入")

        # 原料设置选项卡
        self.materials_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.materials_tab, text="原料设置")

        # 过量设置选项卡
        self.excess_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.excess_tab, text="过量设置")

        self.create_formula_tab()
        self.create_materials_tab()
        self.create_excess_tab()

    def create_formula_tab(self):
        # 化学式输入区域
        formula_frame = ttk.LabelFrame(self.formula_tab, text="化学式和目标质量", padding=15)
        formula_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 示例说明
        example_frame = ttk.Frame(formula_frame)
        example_frame.pack(fill="x", pady=(0, 15))

        ttk.Label(
            example_frame,
            text="示例化学式:",
            font=self.bold_font
        ).pack(anchor="w")

        examples = [
            "(Bi0.5Ba0.1Sr0.1Ca0.2Na0.1)(Fe0.5Ti0.3Zr0.1Nb0.1)O3",
            "0.9(0.55Na0.5Bi0.5TiO3-0.45Ba0.85Ca0.15Zr0.1Ti0.9O3)-0.1Bi(Mg2/3Ta1/3)O3"
        ]

        for example in examples:
            ttk.Label(
                example_frame,
                text=f"• {example}",
                font=self.small_font,
                bootstyle="secondary"
            ).pack(anchor="w", padx=(10, 0))

        # 输入表格
        self.create_formula_input_table(formula_frame)

        # 计算按钮
        button_frame = ttk.Frame(formula_frame)
        button_frame.pack(fill="x", pady=(15, 0))

        self.calculate_button = ttk.Button(
            button_frame,
            text="开始计算",
            command=self.calculate_materials,
            bootstyle="primary",
            width=20
        )
        self.calculate_button.pack(side="right")

        ttk.Button(
            button_frame,
            text="添加行",
            command=self.add_formula_row,
            bootstyle="success-outline",
            width=15
        ).pack(side="right", padx=(0, 10))

    def create_formula_input_table(self, parent):
        # 表格容器
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill="both", expand=True)

        # 表头
        header_frame = ttk.Frame(table_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(header_frame, text="化学式", font=self.bold_font, width=40).pack(side="left", padx=5)
        ttk.Label(header_frame, text="目标质量(g)", font=self.bold_font, width=15).pack(side="left", padx=5)
        ttk.Label(header_frame, text="操作", font=self.bold_font, width=10).pack(side="left", padx=5)

        # 滚动容器
        canvas = ttk.Canvas(table_frame, height=300)
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=canvas.yview)
        self.formula_scrollable_frame = ttk.Frame(canvas)

        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.create_window((0, 0), window=self.formula_scrollable_frame, anchor="nw")

        def update_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        self.formula_scrollable_frame.bind("<Configure>", update_scroll_region)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 添加第一行
        self.add_formula_row()

    def add_formula_row(self):
        row_frame = ttk.Frame(self.formula_scrollable_frame)
        row_frame.pack(fill="x", pady=2)

        # 化学式输入
        formula_entry = ttk.Entry(row_frame, font=self.default_font, width=50)
        formula_entry.pack(side="left", padx=5)

        # 默认值
        if len(self.formula_entries) == 0:
            formula_entry.insert(0, "(Bi0.5Ba0.1Sr0.1Ca0.2Na0.1)(Fe0.5Ti0.3Zr0.1Nb0.1)O3")

        # 质量输入
        mass_entry = ttk.Entry(row_frame, font=self.default_font, width=15)
        mass_entry.pack(side="left", padx=5)

        if len(self.mass_entries) == 0:
            mass_entry.insert(0, "50")

        # 删除按钮
        if len(self.formula_entries) > 0:
            delete_btn = ttk.Button(
                row_frame,
                text="删除",
                command=lambda: self.delete_formula_row(row_frame, formula_entry, mass_entry),
                bootstyle="danger-outline",
                width=8
            )
            delete_btn.pack(side="left", padx=5)

        self.formula_entries.append(formula_entry)
        self.mass_entries.append(mass_entry)

    def delete_formula_row(self, row_frame, formula_entry, mass_entry):
        if formula_entry in self.formula_entries:
            idx = self.formula_entries.index(formula_entry)
            self.formula_entries.pop(idx)
            self.mass_entries.pop(idx)
        row_frame.destroy()

    def create_materials_tab(self):
        # 原料设置说明
        info_frame = ttk.Frame(self.materials_tab)
        info_frame.pack(fill="x", padx=10, pady=10)

        ttk.Label(
            info_frame,
            text="原料信息设置",
            font=self.bold_font
        ).pack(anchor="w")

        ttk.Label(
            info_frame,
            text="设置每个元素对应的原料化学式、纯度和货号信息",
            font=self.small_font,
            bootstyle="secondary"
        ).pack(anchor="w", pady=(5, 0))

        # 原料表格
        self.create_materials_table()

    def create_materials_table(self):
        # 表格容器
        table_container = ttk.Frame(self.materials_tab)
        table_container.pack(fill="both", expand=True, padx=10, pady=10)

        # 表头
        header_frame = ttk.Frame(table_container)
        header_frame.pack(fill="x", pady=(0, 5))

        headers = ["元素", "原料化学式", "纯度", "货号"]
        widths = [8, 15, 8, 12]

        for header, width in zip(headers, widths):
            ttk.Label(
                header_frame,
                text=header,
                font=self.bold_font,
                width=width
            ).pack(side="left", padx=5)

        # 滚动容器
        canvas = ttk.Canvas(table_container)
        scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        def update_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        scrollable_frame.bind("<Configure>", update_scroll_region)

        # 存储输入框引用
        self.material_entries = {}

        # 常用元素
        common_elements = ['Bi', 'Na', 'Ti', 'Ta', 'Ba', 'Ca', 'Zr', 'Sr', 'La', 'Fe', 'Mg', 'K', 'Li', 'Al', 'Si', 'Nb']

        for element in common_elements:
            self.create_material_row(scrollable_frame, element)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 保存按钮
        save_frame = ttk.Frame(self.materials_tab)
        save_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(
            save_frame,
            text="保存原料设置",
            command=self.save_materials,
            bootstyle="success",
            width=20
        ).pack(side="right")

    def create_material_row(self, parent, element):
        row_frame = ttk.Frame(parent)
        row_frame.pack(fill="x", pady=2)

        # 元素名称
        ttk.Label(row_frame, text=element, font=self.default_font, width=8).pack(side="left", padx=5)

        # 原料化学式
        formula_entry = ttk.Entry(row_frame, font=self.default_font, width=15)
        formula_entry.pack(side="left", padx=5)

        # 纯度
        purity_entry = ttk.Entry(row_frame, font=self.default_font, width=8)
        purity_entry.pack(side="left", padx=5)

        # 货号
        code_entry = ttk.Entry(row_frame, font=self.default_font, width=12)
        code_entry.pack(side="left", padx=5)

        # 填入默认值
        if element in self.raw_materials:
            formula, purity, code = self.raw_materials[element]
            formula_entry.insert(0, formula)
            purity_entry.insert(0, str(purity))
            code_entry.insert(0, code)

        self.material_entries[element] = (formula_entry, purity_entry, code_entry)

    def create_excess_tab(self):
        # 过量设置说明
        info_frame = ttk.Frame(self.excess_tab)
        info_frame.pack(fill="x", padx=10, pady=10)

        ttk.Label(
            info_frame,
            text="元素过量设置",
            font=self.bold_font
        ).pack(anchor="w")

        ttk.Label(
            info_frame,
            text="设置需要过量的元素及其过量倍数（如1.1表示过量10%）",
            font=self.small_font,
            bootstyle="secondary"
        ).pack(anchor="w", pady=(5, 0))

        # 过量设置表格
        self.create_excess_table()

    def create_excess_table(self):
        # 表格容器
        table_container = ttk.Frame(self.excess_tab)
        table_container.pack(fill="both", expand=True, padx=10, pady=10)

        # 表头
        header_frame = ttk.Frame(table_container)
        header_frame.pack(fill="x", pady=(0, 5))

        headers = ["元素", "是否过量", "过量倍数"]
        widths = [10, 12, 12]

        for header, width in zip(headers, widths):
            ttk.Label(
                header_frame,
                text=header,
                font=self.bold_font,
                width=width
            ).pack(side="left", padx=5)

        # 滚动容器
        canvas = ttk.Canvas(table_container)
        scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        def update_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        scrollable_frame.bind("<Configure>", update_scroll_region)

        # 存储过量设置
        self.excess_vars = {}
        self.excess_entries = {}

        # 常用元素（排除氧）
        common_elements = [elem for elem in ['Bi', 'Na', 'Ti', 'Ta', 'Ba', 'Ca', 'Zr', 'Sr', 'La', 'Fe', 'Mg', 'K', 'Li', 'Al', 'Si', 'Nb'] if elem != 'O']

        for element in common_elements:
            self.create_excess_row(scrollable_frame, element)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 保存按钮
        save_frame = ttk.Frame(self.excess_tab)
        save_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(
            save_frame,
            text="保存过量设置",
            command=self.save_excess,
            bootstyle="success",
            width=20
        ).pack(side="right")

    def create_excess_row(self, parent, element):
        row_frame = ttk.Frame(parent)
        row_frame.pack(fill="x", pady=2)

        # 元素名称
        ttk.Label(row_frame, text=element, font=self.default_font, width=10).pack(side="left", padx=5)

        # 是否过量复选框
        self.excess_vars[element] = ttk.BooleanVar()
        excess_cb = ttk.Checkbutton(
            row_frame,
            variable=self.excess_vars[element]
        )
        excess_cb.pack(side="left", padx=5)

        # 过量倍数
        excess_entry = ttk.Entry(row_frame, font=self.default_font, width=12)
        excess_entry.insert(0, "1.1")
        excess_entry.pack(side="left", padx=5)

        self.excess_entries[element] = excess_entry

    def create_right_panel(self):
        # 结果显示区域
        result_frame = ttk.LabelFrame(self.right_panel, text="计算结果", padding=15)
        result_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 工具栏
        toolbar = ttk.Frame(result_frame)
        toolbar.pack(fill="x", pady=(0, 10))

        self.export_button = ttk.Button(
            toolbar,
            text="导出Excel",
            command=self.export_to_excel,
            bootstyle="success-outline",
            width=15
        )
        self.export_button.pack(side="right")
        self.export_button.pack_forget()  # 初始隐藏

        # 结果表格容器
        self.result_container = ttk.Frame(result_frame)
        self.result_container.pack(fill="both", expand=True)

        # 初始提示
        self.show_initial_message()

    def show_initial_message(self):
        message_frame = ttk.Frame(self.result_container)
        message_frame.pack(expand=True)

        ttk.Label(
            message_frame,
            text="请输入化学式和目标质量，然后点击\"开始计算\"",
            font=self.default_font,
            bootstyle="secondary"
        ).pack(pady=50)

        instructions = [
            "1. 在左侧\"化学式输入\"选项卡中输入化学式和目标质量",
            "2. 在\"原料设置\"选项卡中配置原料信息",
            "3. 在\"过量设置\"选项卡中设置需要过量的元素",
            "4. 点击\"开始计算\"按钮获得配料结果"
        ]

        for instruction in instructions:
            ttk.Label(
                message_frame,
                text=instruction,
                font=self.small_font,
                bootstyle="secondary"
            ).pack(anchor="w", pady=2)

    def save_materials(self):
        """保存原料设置"""
        try:
            new_materials = {}
            for element, (formula_entry, purity_entry, code_entry) in self.material_entries.items():
                formula = formula_entry.get().strip()
                purity = purity_entry.get().strip()
                code = code_entry.get().strip()

                if formula and purity:
                    try:
                        purity_value = float(purity)
                        if not 0 < purity_value <= 1:
                            raise ValueError
                        new_materials[element] = (formula, purity_value, code)
                    except ValueError:
                        messagebox.showerror("输入错误", f"元素 {element} 的纯度必须是0到1之间的数字")
                        return

            self.raw_materials.update(new_materials)
            messagebox.showinfo("保存成功", "原料设置已保存")

        except Exception as e:
            messagebox.showerror("保存错误", f"保存原料设置时出错: {str(e)}")

    def save_excess(self):
        """保存过量设置"""
        try:
            new_excess = {}
            for element, var in self.excess_vars.items():
                if var.get():
                    try:
                        excess_value = float(self.excess_entries[element].get().strip())
                        if excess_value <= 0:
                            raise ValueError
                        new_excess[element] = excess_value
                    except ValueError:
                        messagebox.showerror("输入错误", f"元素 {element} 的过量倍数必须是正数")
                        return

            self.excess_settings = new_excess
            messagebox.showinfo("保存成功", "过量设置已保存")

        except Exception as e:
            messagebox.showerror("保存错误", f"保存过量设置时出错: {str(e)}")

    def parse_chemical_formula(self, formula: str) -> Dict[str, float]:
        """解析化学式"""
        try:
            def parse_part(part: str, coef: float = 1.0) -> Dict[str, float]:
                """递归解析化学式的一部分"""
                result = {}
                current_element = ''
                current_number = ''
                i = 0

                while i < len(part):
                    char = part[i]

                    if char.isupper():
                        if current_element:
                            number = float(current_number) if current_number else 1.0
                            if current_element in result:
                                result[current_element] += number
                            else:
                                result[current_element] = number
                        current_element = char
                        current_number = ''

                    elif char.islower():
                        current_element += char

                    elif char.isdigit() or char == '.':
                        current_number += char

                    elif char == '(':
                        if current_element:
                            number = float(current_number) if current_number else 1.0
                            if current_element in result:
                                result[current_element] += number
                            else:
                                result[current_element] = number
                        current_element = ''
                        current_number = ''

                        # 找到对应的右括号
                        bracket_count = 1
                        j = i + 1
                        while j < len(part) and bracket_count > 0:
                            if part[j] == '(':
                                bracket_count += 1
                            elif part[j] == ')':
                                bracket_count -= 1
                            j += 1

                        # 获取括号内容
                        inner_content = part[i+1:j-1]

                        # 获取括号后的系数
                        k = j
                        bracket_number = ''
                        while k < len(part) and (part[k].isdigit() or part[k] == '.'):
                            bracket_number += part[k]
                            k += 1

                        bracket_coef = float(bracket_number) if bracket_number else 1.0

                        # 递归处理括号内容
                        inner_result = parse_part(inner_content, bracket_coef)
                        for elem, count in inner_result.items():
                            if elem in result:
                                result[elem] += count
                            else:
                                result[elem] = count

                        i = k - 1

                    i += 1

                # 处理最后一个元素
                if current_element:
                    number = float(current_number) if current_number else 1.0
                    if current_element in result:
                        result[current_element] += number
                    else:
                        result[current_element] = number

                # 应用系数
                return {k: v * coef for k, v in result.items()}

            # 提取主系数
            match = re.match(r'^(\d*\.?\d*)', formula)
            if match and match.group(1):
                main_coef = float(match.group(1))
                formula = formula[len(match.group(1)):]
            else:
                main_coef = 1.0

            # 解析整个化学式
            result = parse_part(formula, main_coef)
            return result

        except Exception as e:
            raise ValueError(f"化学式格式错误: {formula}\n详细错误: {str(e)}")

    def calculate_compound_mass(self, compound: str) -> float:
        """计算化合物的摩尔质量"""
        elements = re.findall(r'([A-Z][a-z]*)(\d*)', compound)
        mass = 0
        for element, count in elements:
            count = int(count) if count else 1
            if element not in self.atomic_weights:
                raise ValueError(f"未知元素: {element}")
            mass += self.atomic_weights[element] * count
        return mass

    def calculate_raw_materials(self, element_moles: Dict[str, float], target_mass: float) -> Dict[str, float]:
        """计算原料用量"""
        try:
            raw_material_masses = {}
            total_molar_mass = 0

            for element, moles in element_moles.items():
                if element == 'O':  # 跳过氧元素
                    continue

                if element not in self.raw_materials:
                    raise ValueError(f"未设置元素 {element} 的原料信息，请在原料设置中配置")

                material, purity, _ = self.raw_materials[element]

                # 获取过量系数
                excess = self.excess_settings.get(element, 1.0)

                # 计算原料的摩尔质量
                material_molar_mass = self.calculate_compound_mass(material)

                # 计算质量
                mass = moles * material_molar_mass / purity * excess
                raw_material_masses[material] = mass
                total_molar_mass += mass

            # 归一化到目标质量
            if total_molar_mass == 0:
                raise ValueError("计算出的总质量为0，请检查化学式和原料设置")

            scale_factor = target_mass / total_molar_mass
            return {k: v * scale_factor for k, v in raw_material_masses.items()}

        except Exception as e:
            raise ValueError(f"原料计算错误: {str(e)}")

    def calculate_materials(self):
        """执行配料计算"""
        try:
            # 检查输入
            if not self.formula_entries:
                messagebox.showerror("输入错误", "请添加至少一个化学式")
                return

            # 收集输入数据
            formulas = []
            masses = []
            for i, formula_entry in enumerate(self.formula_entries):
                formula = formula_entry.get().strip()
                if not formula:
                    messagebox.showerror("输入错误", f"第 {i+1} 行的化学式不能为空")
                    return

                try:
                    mass = float(self.mass_entries[i].get().strip())
                    if mass <= 0:
                        raise ValueError
                except ValueError:
                    messagebox.showerror("输入错误", f"第 {i+1} 行的目标质量必须是正数")
                    return

                formulas.append(formula)
                masses.append(mass)

            # 计算每个化学式的配料
            all_results = []
            all_materials = set()

            for formula, target_mass in zip(formulas, masses):
                try:
                    element_moles = self.parse_chemical_formula(formula)
                    raw_material_masses = self.calculate_raw_materials(element_moles, target_mass)

                    all_materials.update(raw_material_masses.keys())
                    all_results.append({
                        'formula': formula,
                        'target_mass': target_mass,
                        'materials': raw_material_masses
                    })
                except Exception as e:
                    messagebox.showerror("计算错误", f"计算化学式 '{formula}' 时出错: {str(e)}")
                    return

            # 存储结果
            self.calculation_results = {
                'results': all_results,
                'all_materials': sorted(list(all_materials))
            }

            # 显示结果
            self.show_results(all_results, all_materials)

        except Exception as e:
            messagebox.showerror("计算错误", str(e))

    def show_results(self, results, materials):
        """显示计算结果"""
        # 清空结果容器
        for widget in self.result_container.winfo_children():
            widget.destroy()

        # 创建结果表格
        self.create_result_table(results, materials)

        # 显示导出按钮
        self.export_button.pack(side="right")

    def create_result_table(self, results, materials):
        """创建结果表格"""
        # 表格容器
        table_frame = ttk.Frame(self.result_container)
        table_frame.pack(fill="both", expand=True)

        # 创建Treeview表格
        columns = ["原料/化学式"] + [f"配方{i+1}" for i in range(len(results))] + ["总计(g)"]

        self.result_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        for col in columns:
            self.result_tree.heading(col, text=col)
            if col == "原料/化学式":
                self.result_tree.column(col, width=150, anchor="w")
            else:
                self.result_tree.column(col, width=120, anchor="center")

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.result_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.result_tree.xview)
        self.result_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局
        self.result_tree.pack(side="left", fill="both", expand=True)
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")

        # 添加目标质量行
        mass_row = ["目标质量(g)"]
        total_mass = 0
        for result in results:
            mass = result['target_mass']
            total_mass += mass
            mass_row.append(f"{mass:.2f}")
        mass_row.append(f"{total_mass:.2f}")

        self.result_tree.insert("", "end", values=mass_row, tags=("header",))

        # 计算每种原料的总量
        material_totals = {}
        for material in materials:
            material_totals[material] = 0
            for result in results:
                if material in result['materials']:
                    material_totals[material] += result['materials'][material]

        # 添加原料行
        for material in sorted(materials):
            row = [material]
            for result in results:
                if material in result['materials']:
                    mass = result['materials'][material]
                    row.append(f"{mass:.4f}")
                else:
                    row.append("-")

            # 添加总量
            total = material_totals[material]
            row.append(f"{total:.4f}")

            self.result_tree.insert("", "end", values=row)

        # 设置样式
        self.result_tree.tag_configure("header", background="#e6f3ff", font=self.bold_font)

        # 添加说明文本
        info_frame = ttk.Frame(self.result_container)
        info_frame.pack(fill="x", pady=(10, 0))

        info_text = ttk.Text(info_frame, height=4, wrap="word", font=self.small_font)
        info_text.pack(fill="x")

        info_content = """计算完成！上表显示了每个化学式所需的原料质量。
• 表格中每列对应一个化学式配方
• 每行显示一种原料在各配方中的用量
• 最后一列显示每种原料的总用量
• 点击"导出Excel"可将结果保存为Excel文件"""

        info_text.insert("1.0", info_content)
        info_text.configure(state="disabled")

    def export_to_excel(self):
        """导出计算结果到Excel文件"""
        try:
            if not hasattr(self, 'calculation_results'):
                messagebox.showerror("导出错误", "请先计算配料")
                return

            # 创建保存文件对话框
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel 文件", "*.xlsx")],
                initialfile=f"陶瓷配料计算_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not file_path:
                return

            # 创建Excel数据
            results = self.calculation_results['results']
            materials = self.calculation_results['all_materials']

            # 创建主数据表
            formulas = [result['formula'] for result in results]
            columns = ['原料/化学式'] + [f'配方{i+1}' for i in range(len(results))] + ['总计(g)']

            data = []

            # 目标质量行
            mass_row = ['目标质量(g)']
            total_mass = sum(result['target_mass'] for result in results)
            for result in results:
                mass_row.append(result['target_mass'])
            mass_row.append(total_mass)
            data.append(mass_row)

            # 计算原料总量
            material_totals = {}
            for material in materials:
                material_totals[material] = sum(
                    result['materials'].get(material, 0) for result in results
                )

            # 原料行
            for material in sorted(materials):
                row = [material]
                for result in results:
                    if material in result['materials']:
                        row.append(round(result['materials'][material], 4))
                    else:
                        row.append(0)
                row.append(round(material_totals[material], 4))
                data.append(row)

            # 创建DataFrame并保存
            df = pd.DataFrame(data, columns=columns)

            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 主计算结果
                df.to_excel(writer, sheet_name='配料计算', index=False)

                # 化学式信息
                formula_data = []
                for i, result in enumerate(results):
                    formula_data.append({
                        '配方编号': f'配方{i+1}',
                        '化学式': result['formula'],
                        '目标质量(g)': result['target_mass']
                    })

                formula_df = pd.DataFrame(formula_data)
                formula_df.to_excel(writer, sheet_name='化学式信息', index=False)

                # 原料信息
                material_data = []
                for element, (formula, purity, code) in self.raw_materials.items():
                    material_data.append({
                        '元素': element,
                        '原料': formula,
                        '纯度': purity,
                        '货号': code
                    })

                if material_data:
                    material_df = pd.DataFrame(material_data)
                    material_df.to_excel(writer, sheet_name='原料信息', index=False)

                # 过量设置
                if self.excess_settings:
                    excess_data = []
                    for element, excess in self.excess_settings.items():
                        excess_data.append({
                            '元素': element,
                            '过量倍数': excess
                        })

                    excess_df = pd.DataFrame(excess_data)
                    excess_df.to_excel(writer, sheet_name='过量设置', index=False)

            messagebox.showinfo("导出成功", f"配料计算结果已保存到:\n{file_path}")

        except Exception as e:
            messagebox.showerror("导出错误", f"导出Excel时出错: {str(e)}")

    def run(self):
        """运行应用程序"""
        self.window.mainloop()


if __name__ == "__main__":
    app = ImprovedCeramicCalculator()
    app.run()
