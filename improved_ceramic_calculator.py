import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import re
from typing import Dict
from tkinter import messagebox, filedialog
import json
import os
import pandas as pd
from datetime import datetime


class ImprovedCeramicCalculator:
    def __init__(self):
        # 设置主题和颜色模式
        self.window = ttk.Window(themename="litera")
        self.window.title("储能陶瓷配料计算器")
        self.window.geometry("1400x900")
        self.window.state('zoomed')  # 最大化窗口

        # 设置默认字体
        self.default_font = ("Microsoft YaHei", 11)
        self.bold_font = ("Microsoft YaHei", 11, "bold")
        self.large_font = ("Microsoft YaHei", 16, "bold")
        self.small_font = ("Microsoft YaHei", 10)

        # 原料信息：从Excel文件加载
        self.raw_materials = {}  # {元素: (化学式, 分子量, 纯度, 货号, 过量倍数)}

        # 存储输入框引用
        self.formula_entries = []
        self.mass_entries = []

        # 加载原料数据
        self.load_materials_from_excel()

        self.create_widgets()

    def load_materials_from_excel(self):
        """从Excel文件加载原料数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel('initial.xlsx')

            # 清空现有数据
            self.raw_materials = {}

            # 遍历数据行
            for _, row in df.iterrows():
                element = row.get('Element')
                formula = row.get('Formula')
                molecular_weight = row.get('Molecular Weight')
                purity = row.get('Purity')
                product_no = row.get('Product No.')
                excess = row.get('Excess')

                # 检查必要字段是否存在且不为空
                if (pd.notna(element) and pd.notna(formula) and
                    pd.notna(molecular_weight) and pd.notna(purity)):

                    # 处理纯度（如果是百分比形式，转换为小数）
                    if purity > 1:
                        purity = purity / 100

                    # 处理过量倍数
                    if pd.isna(excess):
                        excess = 1.0

                    # 处理货号
                    if pd.isna(product_no):
                        product_no = ''

                    # 存储原料信息
                    self.raw_materials[element] = (
                        formula,
                        float(molecular_weight),
                        float(purity),
                        str(product_no),
                        float(excess)
                    )

            print(f"成功加载 {len(self.raw_materials)} 种原料数据")

        except FileNotFoundError:
            messagebox.showerror("文件错误", "未找到 initial.xlsx 文件，请确保文件在程序目录下")
            # 使用默认数据
            self.load_default_materials()
        except Exception as e:
            messagebox.showerror("加载错误", f"加载原料数据时出错: {str(e)}")
            # 使用默认数据
            self.load_default_materials()

    def load_default_materials(self):
        """加载默认原料数据"""
        default_data = {
            'Li': ('Li2CO3', 73.89, 0.99, 'L101679-500g', 1.05),
            'Na': ('Na2CO3', 105.99, 0.99, 'NA001', 1.0),
            'Mg': ('MgO', 40.30, 0.99, 'MG001', 1.0),
            'Ca': ('CaCO3', 100.09, 0.99, 'C111980-500g', 1.0),
            'Ti': ('TiO2', 79.87, 0.99, 'T105418-500g', 1.0),
            'Fe': ('Fe2O3', 159.69, 0.99, 'FE001', 1.0),
            'Ba': ('BaCO3', 197.34, 0.99, 'BA001', 1.0),
            'Sr': ('SrCO3', 147.63, 0.99, 'SR001', 1.0),
            'Zr': ('ZrO2', 123.22, 0.99, 'ZR001', 1.0),
            'Nb': ('Nb2O5', 265.81, 0.99, 'NB001', 1.0),
            'Ta': ('Ta2O5', 441.89, 0.99, 'TA001', 1.0),
            'Bi': ('Bi2O3', 465.96, 0.99, 'BI001', 1.0),
            'La': ('La2O3', 325.81, 0.99, 'LA001', 1.0),
            'Al': ('Al2O3', 101.96, 0.99, 'AL001', 1.0),
            'Si': ('SiO2', 60.08, 0.99, 'SI001', 1.0),
            'K': ('K2CO3', 138.21, 0.99, 'K001', 1.0)
        }
        self.raw_materials = default_data

    def create_widgets(self):
        # 创建主容器
        self.main_container = ttk.Frame(self.window)
        self.main_container.pack(fill="both", expand=True, padx=20, pady=20)

        # 创建顶部标题栏
        self.create_header()

        # 创建主要内容区域
        self.content_frame = ttk.Frame(self.main_container)
        self.content_frame.pack(fill="both", expand=True, pady=(20, 0))

        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.content_frame)
        self.notebook.pack(fill="both", expand=True)

        # 化学式输入选项卡
        self.formula_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.formula_tab, text="化学式输入与计算")

        # 原料设置选项卡
        self.settings_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_tab, text="原料设置")

        self.create_formula_tab()
        self.create_settings_tab()

    def create_header(self):
        header = ttk.Frame(self.main_container)
        header.pack(fill="x", pady=(0, 20))

        # 标题
        title = ttk.Label(
            header,
            text="储能陶瓷配料计算器",
            font=self.large_font,
            bootstyle="primary"
        )
        title.pack(side="left")

        # 副标题
        subtitle = ttk.Label(
            header,
            text="精确计算化学配料比例 - 支持复杂化学式解析",
            font=self.default_font,
            bootstyle="secondary"
        )
        subtitle.pack(side="left", padx=(20, 0))

    def create_formula_tab(self):
        # 创建上下分割的容器
        main_paned = ttk.PanedWindow(self.formula_tab, orient="vertical")
        main_paned.pack(fill="both", expand=True, padx=10, pady=10)

        # 上半部分：化学式输入区域
        input_frame = ttk.Frame(main_paned)
        main_paned.add(input_frame, weight=1)

        # 下半部分：结果显示区域
        self.result_frame = ttk.Frame(main_paned)
        main_paned.add(self.result_frame, weight=2)

        # 化学式输入区域
        formula_frame = ttk.LabelFrame(input_frame, text="化学式和目标质量", padding=15)
        formula_frame.pack(fill="both", expand=True)

        # 示例说明
        example_frame = ttk.Frame(formula_frame)
        example_frame.pack(fill="x", pady=(0, 15))

        ttk.Label(
            example_frame,
            text="示例化学式:",
            font=self.bold_font
        ).pack(anchor="w")

        examples = [
            "(Bi0.5Ba0.1Sr0.1Ca0.2Na0.1)(Fe0.5Ti0.3Zr0.1Nb0.1)O3",
            "0.9(0.55Na0.5Bi0.5TiO3-0.45Ba0.85Ca0.15Zr0.1Ti0.9O3)-0.1Bi(Mg2/3Ta1/3)O3"
        ]

        for example in examples:
            ttk.Label(
                example_frame,
                text=f"• {example}",
                font=self.small_font,
                bootstyle="secondary"
            ).pack(anchor="w", padx=(10, 0))

        # 输入表格
        self.create_formula_input_table(formula_frame)

        # 计算按钮
        button_frame = ttk.Frame(formula_frame)
        button_frame.pack(fill="x", pady=(15, 0))

        self.calculate_button = ttk.Button(
            button_frame,
            text="开始计算",
            command=self.calculate_materials,
            bootstyle="primary",
            width=20
        )
        self.calculate_button.pack(side="right")

        ttk.Button(
            button_frame,
            text="添加行",
            command=self.add_formula_row,
            bootstyle="success-outline",
            width=15
        ).pack(side="right", padx=(0, 10))

        # 导出按钮
        self.export_button = ttk.Button(
            button_frame,
            text="导出Excel",
            command=self.export_to_excel,
            bootstyle="success-outline",
            width=15
        )
        self.export_button.pack(side="right", padx=(0, 10))
        self.export_button.pack_forget()  # 初始隐藏

        # 初始化结果显示区域
        self.create_result_area()

    def create_result_area(self):
        """创建结果显示区域"""
        # 结果显示标题
        result_label_frame = ttk.LabelFrame(self.result_frame, text="计算结果", padding=15)
        result_label_frame.pack(fill="both", expand=True)

        # 结果容器
        self.result_container = ttk.Frame(result_label_frame)
        self.result_container.pack(fill="both", expand=True)

        # 初始提示
        self.show_initial_message()

    def show_initial_message(self):
        """显示初始提示信息"""
        # 清空容器
        for widget in self.result_container.winfo_children():
            widget.destroy()

        message_frame = ttk.Frame(self.result_container)
        message_frame.pack(expand=True)

        ttk.Label(
            message_frame,
            text="请输入化学式和目标质量，然后点击\"开始计算\"",
            font=self.default_font,
            bootstyle="secondary"
        ).pack(pady=50)

        instructions = [
            "1. 在上方输入化学式和目标质量",
            "2. 在\"原料设置\"选项卡中查看和修改原料信息",
            "3. 点击\"开始计算\"按钮获得配料结果",
            "4. 计算完成后可导出Excel文件"
        ]

        for instruction in instructions:
            ttk.Label(
                message_frame,
                text=instruction,
                font=self.small_font,
                bootstyle="secondary"
            ).pack(anchor="w", pady=2)

    def create_formula_input_table(self, parent):
        # 表格容器
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill="both", expand=True)

        # 表头
        header_frame = ttk.Frame(table_frame)
        header_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(header_frame, text="化学式", font=self.bold_font, width=40).pack(side="left", padx=5)
        ttk.Label(header_frame, text="目标质量(g)", font=self.bold_font, width=15).pack(side="left", padx=5)
        ttk.Label(header_frame, text="操作", font=self.bold_font, width=10).pack(side="left", padx=5)

        # 滚动容器
        canvas = ttk.Canvas(table_frame, height=300)
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=canvas.yview)
        self.formula_scrollable_frame = ttk.Frame(canvas)

        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.create_window((0, 0), window=self.formula_scrollable_frame, anchor="nw")

        def update_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        self.formula_scrollable_frame.bind("<Configure>", update_scroll_region)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 添加第一行
        self.add_formula_row()

    def add_formula_row(self):
        row_frame = ttk.Frame(self.formula_scrollable_frame)
        row_frame.pack(fill="x", pady=2)

        # 化学式输入
        formula_entry = ttk.Entry(row_frame, font=self.default_font, width=50)
        formula_entry.pack(side="left", padx=5)

        # 默认值
        if len(self.formula_entries) == 0:
            formula_entry.insert(0, "(Bi0.5Ba0.1Sr0.1Ca0.2Na0.1)(Fe0.5Ti0.3Zr0.1Nb0.1)O3")

        # 质量输入
        mass_entry = ttk.Entry(row_frame, font=self.default_font, width=15)
        mass_entry.pack(side="left", padx=5)

        if len(self.mass_entries) == 0:
            mass_entry.insert(0, "50")

        # 删除按钮
        if len(self.formula_entries) > 0:
            delete_btn = ttk.Button(
                row_frame,
                text="删除",
                command=lambda: self.delete_formula_row(row_frame, formula_entry, mass_entry),
                bootstyle="danger-outline",
                width=8
            )
            delete_btn.pack(side="left", padx=5)

        self.formula_entries.append(formula_entry)
        self.mass_entries.append(mass_entry)

    def delete_formula_row(self, row_frame, formula_entry, mass_entry):
        if formula_entry in self.formula_entries:
            idx = self.formula_entries.index(formula_entry)
            self.formula_entries.pop(idx)
            self.mass_entries.pop(idx)
        row_frame.destroy()

    def create_settings_tab(self):
        """创建原料设置选项卡"""
        # 原料设置说明
        info_frame = ttk.Frame(self.settings_tab)
        info_frame.pack(fill="x", padx=10, pady=10)

        ttk.Label(
            info_frame,
            text="原料信息设置",
            font=self.bold_font
        ).pack(anchor="w")

        ttk.Label(
            info_frame,
            text="查看和修改每个元素对应的原料化学式、分子量、纯度、货号和过量倍数",
            font=self.small_font,
            bootstyle="secondary"
        ).pack(anchor="w", pady=(5, 0))

        # 重新加载按钮
        reload_frame = ttk.Frame(info_frame)
        reload_frame.pack(fill="x", pady=(10, 0))

        ttk.Button(
            reload_frame,
            text="重新加载Excel数据",
            command=self.reload_materials,
            bootstyle="info-outline",
            width=20
        ).pack(side="left")

        # 原料表格
        self.create_materials_table()

    def reload_materials(self):
        """重新加载原料数据"""
        self.load_materials_from_excel()
        # 重新创建表格
        self.refresh_materials_table()
        messagebox.showinfo("重新加载", "原料数据已重新加载")

    def create_materials_table(self):
        # 表格容器
        table_container = ttk.Frame(self.settings_tab)
        table_container.pack(fill="both", expand=True, padx=10, pady=10)

        # 表头
        header_frame = ttk.Frame(table_container)
        header_frame.pack(fill="x", pady=(0, 5))

        headers = ["元素", "原料化学式", "分子量", "纯度", "货号", "过量倍数"]
        widths = [8, 15, 12, 8, 15, 10]

        for header, width in zip(headers, widths):
            ttk.Label(
                header_frame,
                text=header,
                font=self.bold_font,
                width=width
            ).pack(side="left", padx=3)

        # 滚动容器
        canvas = ttk.Canvas(table_container)
        scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=canvas.yview)
        self.materials_scrollable_frame = ttk.Frame(canvas)

        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.create_window((0, 0), window=self.materials_scrollable_frame, anchor="nw")

        def update_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        self.materials_scrollable_frame.bind("<Configure>", update_scroll_region)

        # 存储输入框引用
        self.material_entries = {}

        # 创建材料行
        self.create_material_rows()

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 保存按钮
        save_frame = ttk.Frame(self.settings_tab)
        save_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(
            save_frame,
            text="保存原料设置",
            command=self.save_materials,
            bootstyle="success",
            width=20
        ).pack(side="right")

    def create_material_rows(self):
        """创建所有材料行"""
        for element in sorted(self.raw_materials.keys()):
            self.create_material_row(self.materials_scrollable_frame, element)

    def refresh_materials_table(self):
        """刷新材料表格"""
        # 清空现有行
        for widget in self.materials_scrollable_frame.winfo_children():
            widget.destroy()
        self.material_entries = {}

        # 重新创建行
        self.create_material_rows()

    def create_material_row(self, parent, element):
        row_frame = ttk.Frame(parent)
        row_frame.pack(fill="x", pady=2)

        # 元素名称
        ttk.Label(row_frame, text=element, font=self.default_font, width=8).pack(side="left", padx=3)

        # 原料化学式
        formula_entry = ttk.Entry(row_frame, font=self.default_font, width=15)
        formula_entry.pack(side="left", padx=3)

        # 分子量
        mw_entry = ttk.Entry(row_frame, font=self.default_font, width=12)
        mw_entry.pack(side="left", padx=3)

        # 纯度
        purity_entry = ttk.Entry(row_frame, font=self.default_font, width=8)
        purity_entry.pack(side="left", padx=3)

        # 货号
        code_entry = ttk.Entry(row_frame, font=self.default_font, width=15)
        code_entry.pack(side="left", padx=3)

        # 过量倍数
        excess_entry = ttk.Entry(row_frame, font=self.default_font, width=10)
        excess_entry.pack(side="left", padx=3)

        # 填入数据
        if element in self.raw_materials:
            formula, mw, purity, code, excess = self.raw_materials[element]
            formula_entry.insert(0, formula)
            mw_entry.insert(0, str(mw))
            purity_entry.insert(0, str(purity))
            code_entry.insert(0, code)
            excess_entry.insert(0, str(excess))

        self.material_entries[element] = (formula_entry, mw_entry, purity_entry, code_entry, excess_entry)



    def save_materials(self):
        """保存原料设置"""
        try:
            new_materials = {}
            for element, (formula_entry, mw_entry, purity_entry, code_entry, excess_entry) in self.material_entries.items():
                formula = formula_entry.get().strip()
                mw = mw_entry.get().strip()
                purity = purity_entry.get().strip()
                code = code_entry.get().strip()
                excess = excess_entry.get().strip()

                if formula and mw and purity:
                    try:
                        mw_value = float(mw)
                        purity_value = float(purity)
                        excess_value = float(excess) if excess else 1.0

                        # 处理纯度（如果大于1，认为是百分比）
                        if purity_value > 1:
                            purity_value = purity_value / 100

                        if not 0 < purity_value <= 1:
                            raise ValueError(f"纯度必须在0到1之间")
                        if mw_value <= 0:
                            raise ValueError(f"分子量必须大于0")
                        if excess_value <= 0:
                            raise ValueError(f"过量倍数必须大于0")

                        new_materials[element] = (formula, mw_value, purity_value, code, excess_value)
                    except ValueError as e:
                        messagebox.showerror("输入错误", f"元素 {element} 的数据有误: {str(e)}")
                        return

            self.raw_materials.update(new_materials)
            messagebox.showinfo("保存成功", "原料设置已保存")

        except Exception as e:
            messagebox.showerror("保存错误", f"保存原料设置时出错: {str(e)}")

    def parse_chemical_formula(self, formula: str) -> Dict[str, float]:
        """解析化学式"""
        try:
            def parse_part(part: str, coef: float = 1.0) -> Dict[str, float]:
                """递归解析化学式的一部分"""
                result = {}
                current_element = ''
                current_number = ''
                i = 0

                while i < len(part):
                    char = part[i]

                    if char.isupper():
                        if current_element:
                            number = float(current_number) if current_number else 1.0
                            if current_element in result:
                                result[current_element] += number
                            else:
                                result[current_element] = number
                        current_element = char
                        current_number = ''

                    elif char.islower():
                        current_element += char

                    elif char.isdigit() or char == '.':
                        current_number += char

                    elif char == '(':
                        if current_element:
                            number = float(current_number) if current_number else 1.0
                            if current_element in result:
                                result[current_element] += number
                            else:
                                result[current_element] = number
                        current_element = ''
                        current_number = ''

                        # 找到对应的右括号
                        bracket_count = 1
                        j = i + 1
                        while j < len(part) and bracket_count > 0:
                            if part[j] == '(':
                                bracket_count += 1
                            elif part[j] == ')':
                                bracket_count -= 1
                            j += 1

                        # 获取括号内容
                        inner_content = part[i+1:j-1]

                        # 获取括号后的系数
                        k = j
                        bracket_number = ''
                        while k < len(part) and (part[k].isdigit() or part[k] == '.'):
                            bracket_number += part[k]
                            k += 1

                        bracket_coef = float(bracket_number) if bracket_number else 1.0

                        # 递归处理括号内容
                        inner_result = parse_part(inner_content, bracket_coef)
                        for elem, count in inner_result.items():
                            if elem in result:
                                result[elem] += count
                            else:
                                result[elem] = count

                        i = k - 1

                    i += 1

                # 处理最后一个元素
                if current_element:
                    number = float(current_number) if current_number else 1.0
                    if current_element in result:
                        result[current_element] += number
                    else:
                        result[current_element] = number

                # 应用系数
                return {k: v * coef for k, v in result.items()}

            # 提取主系数
            match = re.match(r'^(\d*\.?\d*)', formula)
            if match and match.group(1):
                main_coef = float(match.group(1))
                formula = formula[len(match.group(1)):]
            else:
                main_coef = 1.0

            # 解析整个化学式
            result = parse_part(formula, main_coef)
            return result

        except Exception as e:
            raise ValueError(f"化学式格式错误: {formula}\n详细错误: {str(e)}")

    def calculate_raw_materials(self, element_moles: Dict[str, float], target_mass: float) -> Dict[str, float]:
        """计算原料用量"""
        try:
            raw_material_masses = {}
            total_molar_mass = 0

            for element, moles in element_moles.items():
                if element == 'O':  # 跳过氧元素
                    continue

                if element not in self.raw_materials:
                    raise ValueError(f"未设置元素 {element} 的原料信息，请在原料设置中配置")

                formula, molecular_weight, purity, _, excess = self.raw_materials[element]

                # 计算质量
                mass = moles * molecular_weight / purity * excess
                raw_material_masses[formula] = mass
                total_molar_mass += mass

            # 归一化到目标质量
            if total_molar_mass == 0:
                raise ValueError("计算出的总质量为0，请检查化学式和原料设置")

            scale_factor = target_mass / total_molar_mass
            return {k: v * scale_factor for k, v in raw_material_masses.items()}

        except Exception as e:
            raise ValueError(f"原料计算错误: {str(e)}")

    def calculate_materials(self):
        """执行配料计算"""
        try:
            # 检查输入
            if not self.formula_entries:
                messagebox.showerror("输入错误", "请添加至少一个化学式")
                return

            # 收集输入数据
            formulas = []
            masses = []
            for i, formula_entry in enumerate(self.formula_entries):
                formula = formula_entry.get().strip()
                if not formula:
                    messagebox.showerror("输入错误", f"第 {i+1} 行的化学式不能为空")
                    return

                try:
                    mass = float(self.mass_entries[i].get().strip())
                    if mass <= 0:
                        raise ValueError
                except ValueError:
                    messagebox.showerror("输入错误", f"第 {i+1} 行的目标质量必须是正数")
                    return

                formulas.append(formula)
                masses.append(mass)

            # 计算每个化学式的配料
            all_results = []
            all_materials = set()

            for formula, target_mass in zip(formulas, masses):
                try:
                    element_moles = self.parse_chemical_formula(formula)
                    raw_material_masses = self.calculate_raw_materials(element_moles, target_mass)

                    all_materials.update(raw_material_masses.keys())
                    all_results.append({
                        'formula': formula,
                        'target_mass': target_mass,
                        'materials': raw_material_masses
                    })
                except Exception as e:
                    messagebox.showerror("计算错误", f"计算化学式 '{formula}' 时出错: {str(e)}")
                    return

            # 存储结果
            self.calculation_results = {
                'results': all_results,
                'all_materials': sorted(list(all_materials))
            }

            # 显示结果
            self.show_results(all_results, all_materials)

        except Exception as e:
            messagebox.showerror("计算错误", str(e))

    def show_results(self, results, materials):
        """显示计算结果"""
        # 清空结果容器
        for widget in self.result_container.winfo_children():
            widget.destroy()

        # 创建结果表格
        self.create_result_table(results, materials)

        # 显示导出按钮
        self.export_button.pack(side="right")

    def create_result_table(self, results, materials):
        """创建结果表格"""
        # 表格容器
        table_frame = ttk.Frame(self.result_container)
        table_frame.pack(fill="both", expand=True)

        # 创建Treeview表格
        columns = ["原料/化学式"] + [f"配方{i+1}" for i in range(len(results))] + ["总计(g)"]

        self.result_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        for col in columns:
            self.result_tree.heading(col, text=col)
            if col == "原料/化学式":
                self.result_tree.column(col, width=150, anchor="w")
            else:
                self.result_tree.column(col, width=120, anchor="center")

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.result_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.result_tree.xview)
        self.result_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局
        self.result_tree.pack(side="left", fill="both", expand=True)
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")

        # 添加目标质量行
        mass_row = ["目标质量(g)"]
        total_mass = 0
        for result in results:
            mass = result['target_mass']
            total_mass += mass
            mass_row.append(f"{mass:.2f}")
        mass_row.append(f"{total_mass:.2f}")

        self.result_tree.insert("", "end", values=mass_row, tags=("header",))

        # 计算每种原料的总量
        material_totals = {}
        for material in materials:
            material_totals[material] = 0
            for result in results:
                if material in result['materials']:
                    material_totals[material] += result['materials'][material]

        # 添加原料行
        for material in sorted(materials):
            row = [material]
            for result in results:
                if material in result['materials']:
                    mass = result['materials'][material]
                    row.append(f"{mass:.4f}")
                else:
                    row.append("-")

            # 添加总量
            total = material_totals[material]
            row.append(f"{total:.4f}")

            self.result_tree.insert("", "end", values=row)

        # 设置样式
        self.result_tree.tag_configure("header", background="#e6f3ff", font=self.bold_font)

        # 添加说明文本
        info_frame = ttk.Frame(self.result_container)
        info_frame.pack(fill="x", pady=(10, 0))

        info_text = ttk.Text(info_frame, height=4, wrap="word", font=self.small_font)
        info_text.pack(fill="x")

        info_content = """计算完成！上表显示了每个化学式所需的原料质量。
• 表格中每列对应一个化学式配方
• 每行显示一种原料在各配方中的用量
• 最后一列显示每种原料的总用量
• 点击"导出Excel"可将结果保存为Excel文件"""

        info_text.insert("1.0", info_content)
        info_text.configure(state="disabled")

    def export_to_excel(self):
        """导出计算结果到Excel文件"""
        try:
            if not hasattr(self, 'calculation_results'):
                messagebox.showerror("导出错误", "请先计算配料")
                return

            # 创建保存文件对话框
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel 文件", "*.xlsx")],
                initialfile=f"陶瓷配料计算_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not file_path:
                return

            # 创建Excel数据
            results = self.calculation_results['results']
            materials = self.calculation_results['all_materials']

            # 创建主数据表
            formulas = [result['formula'] for result in results]
            columns = ['原料/化学式'] + [f'配方{i+1}' for i in range(len(results))] + ['总计(g)']

            data = []

            # 目标质量行
            mass_row = ['目标质量(g)']
            total_mass = sum(result['target_mass'] for result in results)
            for result in results:
                mass_row.append(result['target_mass'])
            mass_row.append(total_mass)
            data.append(mass_row)

            # 计算原料总量
            material_totals = {}
            for material in materials:
                material_totals[material] = sum(
                    result['materials'].get(material, 0) for result in results
                )

            # 原料行
            for material in sorted(materials):
                row = [material]
                for result in results:
                    if material in result['materials']:
                        row.append(round(result['materials'][material], 4))
                    else:
                        row.append(0)
                row.append(round(material_totals[material], 4))
                data.append(row)

            # 创建DataFrame并保存
            df = pd.DataFrame(data, columns=columns)

            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 主计算结果
                df.to_excel(writer, sheet_name='配料计算', index=False)

                # 化学式信息
                formula_data = []
                for i, result in enumerate(results):
                    formula_data.append({
                        '配方编号': f'配方{i+1}',
                        '化学式': result['formula'],
                        '目标质量(g)': result['target_mass']
                    })

                formula_df = pd.DataFrame(formula_data)
                formula_df.to_excel(writer, sheet_name='化学式信息', index=False)

                # 原料信息
                material_data = []
                for element, (formula, mw, purity, code, excess) in self.raw_materials.items():
                    material_data.append({
                        '元素': element,
                        '原料': formula,
                        '分子量': mw,
                        '纯度': purity,
                        '货号': code,
                        '过量倍数': excess
                    })

                if material_data:
                    material_df = pd.DataFrame(material_data)
                    material_df.to_excel(writer, sheet_name='原料信息', index=False)

            messagebox.showinfo("导出成功", f"配料计算结果已保存到:\n{file_path}")

        except Exception as e:
            messagebox.showerror("导出错误", f"导出Excel时出错: {str(e)}")

    def run(self):
        """运行应用程序"""
        self.window.mainloop()


if __name__ == "__main__":
    app = ImprovedCeramicCalculator()
    app.run()
