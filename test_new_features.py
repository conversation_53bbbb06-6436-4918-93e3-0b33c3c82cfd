#!/usr/bin/env python3
"""
测试新功能：Excel加载和模板下载
"""

import pandas as pd
import os

def test_excel_loading():
    """测试Excel文件加载功能"""
    print("=== 测试Excel文件加载功能 ===")
    
    # 检查initial.xlsx文件
    if os.path.exists('initial.xlsx'):
        print("✓ initial.xlsx 文件存在")
        
        # 读取并显示文件内容
        df = pd.read_excel('initial.xlsx')
        print(f"✓ 文件包含 {len(df)} 行数据")
        print(f"✓ 列名: {list(df.columns)}")
        
        # 显示有效数据行数
        valid_rows = df.dropna(subset=['Element', 'Formula', 'Molecular Weight', 'Purity'])
        print(f"✓ 有效数据行数: {len(valid_rows)}")
        
        # 显示前几行数据
        print("\n前5行数据:")
        print(valid_rows.head().to_string())
        
        return True
    else:
        print("✗ initial.xlsx 文件不存在")
        return False

def test_template_creation():
    """测试模板创建功能"""
    print("\n=== 测试模板创建功能 ===")
    
    # 创建模板数据
    template_data = {
        '<PERSON>ement No.': [3, 11, 12, 19, 20, 22, 26, 38, 40, 41, 56, 57, 73, 83],
        '<PERSON>ement': ['<PERSON>', 'Na', 'Mg', 'K', 'Ca', 'Ti', '<PERSON>', 'Sr', 'Zr', 'Nb', 'Ba', '<PERSON>', 'Ta', 'Bi'],
        '<PERSON>io': [2.0, 2.0, 1.0, 2.0, 1.0, 2.0, 2.0, 1.0, 1.0, 2.0, 1.0, 2.0, 2.0, 2.0],
        'Formula': ['Li2CO3', 'Na2CO3', 'MgO', 'K2CO3', 'CaCO3', 'TiO2', 'Fe2O3', 'SrCO3', 'ZrO2', 'Nb2O5', 'BaCO3', 'La2O3', 'Ta2O5', 'Bi2O3'],
        'Molecular Weight': [73.89, 105.99, 40.30, 138.21, 100.09, 79.87, 159.69, 147.63, 123.22, 265.81, 197.34, 325.81, 441.89, 465.96],
        'CAS': ['554-13-2', '497-19-8', '1309-48-4', '584-08-7', '471-34-1', '13463-67-7', '1309-37-1', '1633-05-2', '1314-23-4', '1313-96-8', '513-77-9', '1312-81-8', '1314-61-0', '1304-76-3'],
        'Manufacturer': ['aladdin'] * 14,
        'Product No.': ['L101679-500g', 'S104993-500g', 'M104467-500g', 'P112089-500g', 'C111980-500g', 'T105418-500g', 'F104317-500g', 'S116738-500g', 'Z104058-500g', 'N104467-500g', 'B101467-500g', 'L104467-500g', 'T104467-500g', 'B104467-500g'],
        'Purity': [99.0] * 14,
        'Pricing': [849.9, 58.9, 78.9, 89.9, 58.9, 77.9, 68.9, 189.9, 289.9, 1289.9, 78.9, 589.9, 2589.9, 189.9],
        'Excess': [1.05, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    }
    
    try:
        # 创建DataFrame
        df = pd.DataFrame(template_data)
        print(f"✓ 模板包含 {len(df)} 行数据")
        print(f"✓ 模板列名: {list(df.columns)}")
        
        # 保存模板
        template_file = "原料数据模板_测试.xlsx"
        df.to_excel(template_file, index=False)
        print(f"✓ 模板已保存为: {template_file}")
        
        # 验证保存的文件
        if os.path.exists(template_file):
            test_df = pd.read_excel(template_file)
            print(f"✓ 验证: 保存的文件包含 {len(test_df)} 行数据")
            return True
        else:
            print("✗ 模板文件保存失败")
            return False
            
    except Exception as e:
        print(f"✗ 创建模板时出错: {e}")
        return False

def test_calculator_integration():
    """测试计算器集成"""
    print("\n=== 测试计算器集成 ===")
    
    try:
        # 导入计算器（不启动GUI）
        import sys
        sys.path.append('.')
        
        # 这里我们只测试数据结构，不启动GUI
        print("✓ 计算器模块可以导入")
        
        # 测试Excel数据解析
        if os.path.exists('initial.xlsx'):
            df = pd.read_excel('initial.xlsx')
            valid_data = {}
            
            for _, row in df.iterrows():
                element = row.get('Element')
                formula = row.get('Formula')
                molecular_weight = row.get('Molecular Weight')
                purity = row.get('Purity')
                product_no = row.get('Product No.')
                excess = row.get('Excess')
                
                if (pd.notna(element) and pd.notna(formula) and 
                    pd.notna(molecular_weight) and pd.notna(purity)):
                    
                    purity_value = float(purity)
                    if purity_value > 1:
                        purity_value = purity_value / 100
                    
                    if pd.isna(excess):
                        excess = 1.0
                    
                    if pd.isna(product_no):
                        product_no = ''
                    
                    valid_data[element] = (
                        str(formula), 
                        float(molecular_weight), 
                        float(purity_value), 
                        str(product_no), 
                        float(excess)
                    )
            
            print(f"✓ 成功解析 {len(valid_data)} 种原料数据")
            
            # 显示前几个原料
            for i, (element, data) in enumerate(valid_data.items()):
                if i < 3:
                    formula, mw, purity, code, excess = data
                    print(f"  {element}: {formula}, MW={mw}, 纯度={purity}, 过量={excess}")
            
            return True
        else:
            print("✗ initial.xlsx 文件不存在，无法测试")
            return False
            
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试新功能...\n")
    
    # 运行所有测试
    test1 = test_excel_loading()
    test2 = test_template_creation()
    test3 = test_calculator_integration()
    
    print(f"\n=== 测试结果 ===")
    print(f"Excel加载测试: {'✓ 通过' if test1 else '✗ 失败'}")
    print(f"模板创建测试: {'✓ 通过' if test2 else '✗ 失败'}")
    print(f"集成测试: {'✓ 通过' if test3 else '✗ 失败'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 所有测试通过！")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能")
