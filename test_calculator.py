#!/usr/bin/env python3
"""
测试重构后的陶瓷配料计算器
"""

import pandas as pd
from improved_ceramic_calculator import ImprovedCeramicCalculator

def test_excel_loading():
    """测试Excel文件加载"""
    print("测试Excel文件加载...")
    
    # 创建计算器实例
    calc = ImprovedCeramicCalculator()
    
    # 检查是否成功加载了原料数据
    print(f"加载的原料数量: {len(calc.raw_materials)}")
    
    # 显示前几个原料
    for i, (element, data) in enumerate(calc.raw_materials.items()):
        if i < 5:  # 只显示前5个
            formula, mw, purity, code, excess = data
            print(f"{element}: {formula}, MW={mw}, 纯度={purity}, 货号={code}, 过量={excess}")
    
    return calc

def test_formula_parsing(calc):
    """测试化学式解析"""
    print("\n测试化学式解析...")
    
    test_formulas = [
        "Li2CO3",
        "CaCO3", 
        "(Bi0.5Na0.5)TiO3"
    ]
    
    for formula in test_formulas:
        try:
            result = calc.parse_chemical_formula(formula)
            print(f"{formula} -> {result}")
        except Exception as e:
            print(f"解析 {formula} 失败: {e}")

def test_calculation(calc):
    """测试配料计算"""
    print("\n测试配料计算...")
    
    # 测试简单化学式
    formula = "Li2CO3"
    target_mass = 10.0
    
    try:
        element_moles = calc.parse_chemical_formula(formula)
        print(f"元素摩尔数: {element_moles}")
        
        raw_materials = calc.calculate_raw_materials(element_moles, target_mass)
        print(f"原料用量: {raw_materials}")
        
    except Exception as e:
        print(f"计算失败: {e}")

if __name__ == "__main__":
    try:
        # 测试Excel加载
        calc = test_excel_loading()
        
        # 测试化学式解析
        test_formula_parsing(calc)
        
        # 测试配料计算
        test_calculation(calc)
        
        print("\n所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
