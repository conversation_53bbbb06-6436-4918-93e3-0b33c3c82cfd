#!/usr/bin/env python3
"""
测试完整加载initial.xlsx的功能
"""

import pandas as pd

def test_complete_loading():
    """测试完整加载功能"""
    print("=== 测试完整加载initial.xlsx ===")
    
    # 首先查看原始Excel文件内容
    try:
        df = pd.read_excel('initial.xlsx')
        print(f"✓ Excel文件包含 {len(df)} 行数据")
        print(f"✓ 列名: {list(df.columns)}")
        
        print("\n所有行的数据:")
        for index, row in df.iterrows():
            element = row.get('Element')
            formula = row.get('Formula')
            molecular_weight = row.get('Molecular Weight')
            purity = row.get('Purity')
            product_no = row.get('Product No.')
            excess = row.get('Excess')
            
            print(f"行 {index+1}: Element={element}, Formula={formula}, MW={molecular_weight}, Purity={purity}, Excess={excess}")
        
        # 模拟新的加载逻辑
        print("\n=== 模拟新的加载逻辑 ===")
        raw_materials = {}
        row_count = 0
        
        for index, row in df.iterrows():
            element = row.get('Element')
            formula = row.get('Formula')
            molecular_weight = row.get('Molecular Weight')
            purity = row.get('Purity')
            product_no = row.get('Product No.')
            excess = row.get('Excess')
            
            # 只要有元素名称就导入
            if pd.notna(element):
                # 处理重复元素：添加序号
                element_key = str(element)
                if element_key in raw_materials:
                    counter = 2
                    while f"{element_key}_{counter}" in raw_materials:
                        counter += 1
                    element_key = f"{element_key}_{counter}"
                
                # 处理各个字段
                formula_value = str(formula) if pd.notna(formula) else ''
                
                if pd.notna(molecular_weight):
                    try:
                        mw_value = float(molecular_weight)
                    except:
                        mw_value = 0.0
                else:
                    mw_value = 0.0
                
                if pd.notna(purity):
                    try:
                        purity_value = float(purity)
                        if purity_value > 1:
                            purity_value = purity_value / 100
                    except:
                        purity_value = 0.99
                else:
                    purity_value = 0.99
                
                if pd.notna(excess):
                    try:
                        excess_value = float(excess)
                    except:
                        excess_value = 1.0
                else:
                    excess_value = 1.0
                
                product_value = str(product_no) if pd.notna(product_no) else ''
                
                # 存储原料信息
                raw_materials[element_key] = (
                    formula_value, 
                    mw_value, 
                    purity_value, 
                    product_value, 
                    excess_value
                )
                row_count += 1
                
                print(f"✓ 加载: {element_key} -> {formula_value}, MW={mw_value}, 纯度={purity_value}, 过量={excess_value}")
        
        print(f"\n✓ 总共加载了 {row_count} 行数据，包含 {len(raw_materials)} 种原料")
        
        # 显示最终结果
        print("\n=== 最终加载的原料数据 ===")
        for key, data in raw_materials.items():
            formula, mw, purity, code, excess = data
            print(f"{key}: {formula} (MW={mw}, 纯度={purity}, 过量={excess})")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_complete_loading()
